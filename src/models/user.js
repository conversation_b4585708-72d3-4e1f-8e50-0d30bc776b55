import { parseStringPromise } from 'xml2js';
import { Office } from './office.js';

export class User {
  /** @type {string} */
  id;
  /** @type {string} */
  externalId;
  /** @type {string} */
  name;
  /** @type {string} */
  ntUsername;
  /** @type {string} */
  qualifications;
  /** @type {string} */
  email;
  /** @type {string} */
  target;
  /** @type {string} */
  prevTarget;
  /** @type {string[]} */
  roles;
  /** @type {boolean} */
  valuer;
  /** @type {boolean} */
  admin;
  /** @type {boolean} */
  registeredValuer;
  /** @type {boolean} */
  canValueRatingUnit;
  /** @type {Office} */
  office;

  constructor(data = {}) {
    Object.assign(this, data);

    this.roles = this.roles || [];
    this.valuer = this.valuer || false;
    this.admin = this.admin || false;
    this.registeredValuer = this.registeredValuer || false;
    this.canValueRatingUnit = this.canValueRatingUnit || false;

    if (data.office) {
      this.office = data.office instanceof Office ? data.office : new Office(data.office);
    } else {
      this.office = new Office();
    }
  }


  static fromDatabaseRecordset(recordset) {
    const row = recordset[0];
    const user = new User();

    user.id = row.id;
    user.externalId = null;
    user.name = row.name;
    user.ntUsername = row.nt_username;
    user.qualifications = row.qualifications;
    user.email = row.email;
    user.target = row.target_role;
    user.prevTarget = row.h_target_role;
    user.roles = [];
    user.valuer = false;
    user.admin = false;
    user.registeredValuer = false;
    user.canValueRatingUnit = false;

    user.office = Office.fromDatabaseRow({
      id: row.office_id,
      name: row.office_name,
      phone_number: row.phone_number,
      fax_number: row.fax_number,
      address_line_1: row.address_line_1,
      address_line_2: row.address_line_2,
      address_line_3: row.address_line_3,
      address_line_4: row.address_line_4,
      address_line_5: row.address_line_5,
      post_code: row.post_code
    });

    for (const row of recordset) {
      if (row.role && !user.roles.includes(row.role)) {
        user.roles.push(row.role);
      }
    }

    return user;
  }

  static fromDatabaseRecordsets(recordset) {
    const users = [];
    let currentUserId = null;

    for (const row of recordset) {
      if (row.id === currentUserId) {
        if (row.role && !users[users.length - 1].roles.includes(row.role)) {
          users[users.length - 1].roles.push(row.role);
        }
      } else {
        users.push(User.fromDatabaseRecordset([row]));
        currentUserId = row.id;
      }
    }

    return users;
  }

  static async fromXml(xml) {
    const parsed = await parseStringPromise(xml, {
      explicitArray: false,
      mergeAttrs: true,
    });

    const user = new User();
    user.id = null;
    user.externalId = parsed.user?.username || null;
    user.name = parsed.user?.name || null;
    user.ntUsername = parsed.user?.nt_user_name || null;
    user.qualifications = parsed.user?.qualifications || null;
    user.email = parsed.user?.email_address || null;
    user.admin = null;
    user.target = null;
    user.prevTarget = null;
    user.roles = [];

    if (parsed.user.is_registered_valuer_yn === 'true') {
      user.roles.push('Senior Valuer');
    } else if (parsed.user.is_valuer_yn === 'true') {
      user.roles.push('Valuer');
    }

    if (parsed.user.office) {
      const office = new Office();
      const officeData = parsed.user.office;

      office.id = null;
      office.name = officeData.name;
      office.phoneNumber = officeData.phone_number;
      office.faxNumber = officeData.fax_number;
      office.addressLine1 = officeData.address_line_1;
      office.addressLine2 = officeData.address_line_2;
      office.addressLine3 = officeData.address_line_3;
      office.addressLine4 = officeData.address_line_4;
      office.addressLine5 = officeData.address_line_5;
      office.postCode = officeData.post_code;

      user.office = office;
    } else {
      user.office = new Office();
    }

    return user;
  }
}