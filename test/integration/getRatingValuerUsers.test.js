import { describe, it } from 'mocha';
import { expect } from 'chai';
import { getQivsPool } from '../../src/dal/sqlConnectionPool.js';
import { User } from '../../src/models/user.js';
import mssql from 'mssql';
import { Logger } from '@quotable-value/logger';
import { getRatingValuerUsers } from '../../src/dal/userDal.js';

global.logger = new Logger();

describe('getRatingValuerUsers', () => {
  it('should return identical results to stored procedure', async function() {
    this.timeout(60000);

    const pool = await getQivsPool();
    const testOptions = {
      isValuer: true,
      isActive: true,
      pageSize: 5000,
      offset: 0,
      orderBy: 'name',
      desc: false
    };

    const criteriaXml = `
      <criteria>
        <username></username>
        <is_valuer_yn>true</is_valuer_yn>
        <is_registered_valuer_yn></is_registered_valuer_yn>
        <is_active_yn>true</is_active_yn>
      </criteria>
    `;

    console.log('\n=== Performance & Accuracy Test ===');

    const spStartTime = Date.now();
    const storedProcResult = await pool
      .request()
      .input('criteriaXml', mssql.NVarChar, criteriaXml)
      .input('pageSize', mssql.Int, testOptions.pageSize)
      .input('offset', mssql.Int, testOptions.offset)
      .input('orderBy', mssql.VarChar, testOptions.orderBy)
      .input('desc', mssql.Bit, testOptions.desc)
      .execute('spd_MONARCH_searchUsers');

    const storedProcUsers = [];
    for (const row of storedProcResult.recordset) {
      const userXml = row.user_xml;
      const user = await User.fromXml(userXml);
      storedProcUsers.push(user);
    }

    const spDuration = Date.now() - spStartTime;

    const jsStartTime = Date.now();
    const jsResults = await getRatingValuerUsers(testOptions);
    const jsDuration = Date.now() - jsStartTime;

    console.log(`Stored Procedure: ${spDuration}ms (${storedProcUsers.length} results)`);
    console.log(`JavaScript CTE:   ${jsDuration}ms (${jsResults.length} results)`);
    console.log(`Performance Ratio: ${(jsDuration / spDuration).toFixed(2)}x`);

    expect(storedProcUsers.length).to.equal(jsResults.length,
      'Both methods should return the same number of results');

    if (storedProcUsers.length > 0) {
      console.log(`Verifying all ${storedProcUsers.length} records...`);

      for (let i = 0; i < storedProcUsers.length; i++) {
        const spUser = storedProcUsers[i];
        const jsUser = jsResults[i];

        expect(spUser).to.deep.equal(jsUser, `User should be identical at index ${i}`);

        if ((i + 1) % 50 === 0) {
          console.log(`  ✓ Verified ${i + 1}/${storedProcUsers.length} records`);
        }
      }

      console.log(`✅ ALL ${storedProcUsers.length} records verified as identical`);
      console.log(`🚀 JavaScript is ${(spDuration / jsDuration).toFixed(1)}x faster than stored procedure`);
    }
  });
});
