{"name": "api-user", "version": "1.0.0", "type": "module", "description": "api-user service", "scripts": {"start": "npx serverless offline --noAuth", "test": "mocha test/unit --timeout 180000 --reporter mochaw<PERSON>ome", "test:integration": "mocha test/integration/*.test.js --timeout 30000 --reporter mochaw<PERSON>ome", "test:integration:bash": "export ENV=\"local\"; mocha test/integration/*.test.js  --timeout 30000 --reporter mochawesome", "test:integration:ps": "$env:ENV=\"local\"; mocha test/integration/*.test.js --timeout 30000 --reporter mochawesome", "test:single:bash": "export ENV=\"local\" TARGET_BRANCH=\"feature_DEV-6332-unit-tests\"; mocha test/integration/getRatingValuerUsers.test.js --timeout 30000 --reporter mochawesome", "test:smoke": "mocha test/smoke/*.test.js --timeout 30000 --reporter mochaw<PERSON>ome", "test:smoke:bash": "export ENV=\"local\"; mocha test/smoke/*.test.js  --timeout 30000 --reporter mochawesome", "test:smoke:ps": "$env:ENV=\"local\"; mocha test/smoke/*.test.js --timeout 30000 --reporter mochawesome", "cover": "export IS_OFFLINE=true; NODE_OPTIONS='--import ./register-loader.js' nyc mocha test/unit", "lint": "eslint src/**/*.js --fix"}, "repository": {"type": "git", "url": "git+https://github.com/Quotable-Value/api-user.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Quotable-Value/api-user/issues"}, "homepage": "https://github.com/Quotable-Value/api-user#readme", "devDependencies": {"@aws-sdk/client-ssm": "^3.186.0", "@istanbuljs/esm-loader-hook": "^0.2.0", "@quotable-value/serverless": "3.38.1", "@quotable-value/sql-test-container": "1.1.0", "chai": "^4.3.4", "chai-http": "^4.3.0", "chai-json-schema": "^1.5.1", "chai-string": "^1.5.0", "eslint": "^8.57.0", "esmock": "^2.6.6", "mocha": "^10.2.0", "mochawesome": "^7.1.3", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "serverless-domain-manager": "^7.3.8", "serverless-offline": "^13.6.0", "sinon": "^13.0.2", "sinon-chai": "^3.7.0", "testcontainers": "^9.0.0"}, "dependencies": {"@quotable-value/logger": "1.1.0", "mssql": "^10.0.2", "tedious": "^15.1.0", "uuid": "^11.1.0"}}